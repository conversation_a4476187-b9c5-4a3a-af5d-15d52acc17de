using InventoryManagement.Data;
using InventoryManagement.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace InventoryManagement.Services
{
    public class ProductService
    {
        private readonly InventoryContext _context;

        public ProductService()
        {
            _context = new InventoryContext();
        }

        public async Task<List<Product>> GetAllProductsAsync()
        {
            return await _context.Products
                .Include(p => p.Category)
                .OrderBy(p => p.Name)
                .ToListAsync();
        }

        public async Task<Product?> GetProductByIdAsync(int id)
        {
            return await _context.Products
                .Include(p => p.Category)
                .FirstOrDefaultAsync(p => p.Id == id);
        }

        public async Task<Product?> GetProductBySkuAsync(string sku)
        {
            return await _context.Products
                .Include(p => p.Category)
                .FirstOrDefaultAsync(p => p.SKU == sku);
        }

        public async Task<List<Product>> SearchProductsAsync(string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return await GetAllProductsAsync();

            return await _context.Products
                .Include(p => p.Category)
                .Where(p => p.Name.Contains(searchTerm) || 
                           p.SKU.Contains(searchTerm) ||
                           p.Description.Contains(searchTerm))
                .OrderBy(p => p.Name)
                .ToListAsync();
        }

        public async Task<List<Product>> GetProductsByCategoryAsync(int categoryId)
        {
            return await _context.Products
                .Include(p => p.Category)
                .Where(p => p.CategoryId == categoryId)
                .OrderBy(p => p.Name)
                .ToListAsync();
        }

        public async Task<List<Product>> GetLowStockProductsAsync()
        {
            return await _context.Products
                .Include(p => p.Category)
                .Where(p => p.Quantity <= p.MinimumStock)
                .OrderBy(p => p.Quantity)
                .ToListAsync();
        }

        public async Task<bool> AddProductAsync(Product product)
        {
            try
            {
                // Check if SKU already exists
                var existingProduct = await GetProductBySkuAsync(product.SKU);
                if (existingProduct != null)
                    return false;

                product.CreatedDate = DateTime.Now;
                _context.Products.Add(product);
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> UpdateProductAsync(Product product)
        {
            try
            {
                // Check if SKU already exists for another product
                var existingProduct = await _context.Products
                    .FirstOrDefaultAsync(p => p.SKU == product.SKU && p.Id != product.Id);
                if (existingProduct != null)
                    return false;

                product.LastUpdated = DateTime.Now;
                _context.Products.Update(product);
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> DeleteProductAsync(int id)
        {
            try
            {
                var product = await GetProductByIdAsync(id);
                if (product == null)
                    return false;

                // Check if product has transactions
                var hasTransactions = await _context.Transactions
                    .AnyAsync(t => t.ProductId == id);
                
                if (hasTransactions)
                {
                    // Soft delete - mark as inactive
                    product.IsActive = false;
                    _context.Products.Update(product);
                }
                else
                {
                    // Hard delete
                    _context.Products.Remove(product);
                }

                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> UpdateStockAsync(int productId, int newQuantity, string reason, int userId)
        {
            try
            {
                var product = await GetProductByIdAsync(productId);
                if (product == null)
                    return false;

                var oldQuantity = product.Quantity;
                var difference = newQuantity - oldQuantity;

                // Update product quantity
                product.Quantity = newQuantity;
                product.LastUpdated = DateTime.Now;

                // Create transaction record
                var transaction = new Transaction
                {
                    ProductId = productId,
                    Type = difference > 0 ? "IN" : "OUT",
                    Quantity = Math.Abs(difference),
                    UnitPrice = product.PurchasePrice,
                    Reason = reason,
                    TransactionDate = DateTime.Now,
                    UserId = userId,
                    Notes = $"تعديل المخزون من {oldQuantity} إلى {newQuantity}"
                };

                _context.Products.Update(product);
                _context.Transactions.Add(transaction);
                await _context.SaveChangesAsync();

                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<decimal> GetTotalInventoryValueAsync()
        {
            return await _context.Products
                .Where(p => p.IsActive)
                .SumAsync(p => p.Quantity * p.PurchasePrice);
        }

        public async Task<int> GetTotalProductsCountAsync()
        {
            return await _context.Products
                .Where(p => p.IsActive)
                .CountAsync();
        }

        public async Task<int> GetLowStockCountAsync()
        {
            return await _context.Products
                .Where(p => p.IsActive && p.Quantity <= p.MinimumStock)
                .CountAsync();
        }

        public void Dispose()
        {
            _context?.Dispose();
        }
    }
}
