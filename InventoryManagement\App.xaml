<Application x:Class="InventoryManagement.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             StartupUri="Views/LoginWindow.xaml">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- Material Design -->
                <materialDesign:BundledTheme BaseTheme="Light" PrimaryColor="Blue" SecondaryColor="Orange" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />
                
                <!-- Custom Styles -->
                <ResourceDictionary>
                    <!-- Primary Colors -->
                    <SolidColorBrush x:Key="PrimaryBrush" Color="#2196F3"/>
                    <SolidColorBrush x:Key="PrimaryDarkBrush" Color="#1976D2"/>
                    <SolidColorBrush x:Key="AccentBrush" Color="#FF9800"/>
                    <SolidColorBrush x:Key="SuccessBrush" Color="#4CAF50"/>
                    <SolidColorBrush x:Key="WarningBrush" Color="#FF9800"/>
                    <SolidColorBrush x:Key="ErrorBrush" Color="#F44336"/>
                    
                    <!-- Background Colors -->
                    <SolidColorBrush x:Key="BackgroundBrush" Color="#FAFAFA"/>
                    <SolidColorBrush x:Key="SurfaceBrush" Color="#FFFFFF"/>
                    <SolidColorBrush x:Key="CardBrush" Color="#FFFFFF"/>
                    
                    <!-- Text Colors -->
                    <SolidColorBrush x:Key="TextPrimaryBrush" Color="#212121"/>
                    <SolidColorBrush x:Key="TextSecondaryBrush" Color="#757575"/>
                    
                    <!-- Card Style -->
                    <Style x:Key="CardStyle" TargetType="Border">
                        <Setter Property="Background" Value="{StaticResource CardBrush}"/>
                        <Setter Property="CornerRadius" Value="8"/>
                        <Setter Property="Padding" Value="16"/>
                        <Setter Property="Margin" Value="8"/>
                        <Setter Property="Effect">
                            <Setter.Value>
                                <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.3" BlurRadius="8"/>
                            </Setter.Value>
                        </Setter>
                    </Style>
                    
                    <!-- Button Styles -->
                    <Style x:Key="PrimaryButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
                        <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
                        <Setter Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
                        <Setter Property="Foreground" Value="White"/>
                        <Setter Property="Height" Value="36"/>
                        <Setter Property="Margin" Value="4"/>
                        <Setter Property="FontWeight" Value="Medium"/>
                    </Style>
                    
                    <Style x:Key="SecondaryButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignOutlinedButton}">
                        <Setter Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
                        <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
                        <Setter Property="Height" Value="36"/>
                        <Setter Property="Margin" Value="4"/>
                        <Setter Property="FontWeight" Value="Medium"/>
                    </Style>
                    
                    <!-- TextBox Style -->
                    <Style x:Key="ModernTextBoxStyle" TargetType="TextBox" BasedOn="{StaticResource MaterialDesignOutlinedTextBox}">
                        <Setter Property="Margin" Value="4"/>
                        <Setter Property="Height" Value="40"/>
                        <Setter Property="FontSize" Value="14"/>
                    </Style>
                    
                    <!-- ComboBox Style -->
                    <Style x:Key="ModernComboBoxStyle" TargetType="ComboBox" BasedOn="{StaticResource MaterialDesignOutlinedComboBox}">
                        <Setter Property="Margin" Value="4"/>
                        <Setter Property="Height" Value="40"/>
                        <Setter Property="FontSize" Value="14"/>
                    </Style>
                    
                    <!-- DataGrid Style -->
                    <Style x:Key="ModernDataGridStyle" TargetType="DataGrid" BasedOn="{StaticResource MaterialDesignDataGrid}">
                        <Setter Property="AutoGenerateColumns" Value="False"/>
                        <Setter Property="CanUserAddRows" Value="False"/>
                        <Setter Property="CanUserDeleteRows" Value="False"/>
                        <Setter Property="IsReadOnly" Value="True"/>
                        <Setter Property="SelectionMode" Value="Single"/>
                        <Setter Property="GridLinesVisibility" Value="Horizontal"/>
                        <Setter Property="HeadersVisibility" Value="Column"/>
                        <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
                        <Setter Property="RowBackground" Value="{StaticResource SurfaceBrush}"/>
                        <Setter Property="AlternatingRowBackground" Value="#F5F5F5"/>
                    </Style>
                </ResourceDictionary>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Application.Resources>
</Application>
