<Window x:Class="InventoryManagement.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="نظام إدارة المخزون - Stralog SRL"
        Height="800" Width="1200"
        WindowStartupLocation="CenterScreen"
        Background="{StaticResource BackgroundBrush}"
        FlowDirection="RightToLeft">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="{StaticResource PrimaryBrush}" Height="60">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Menu Button -->
                <Button Grid.Column="0" 
                      x:Name="MenuButton"
                      Style="{StaticResource MaterialDesignIconButton}"
                      Foreground="White"
                      Margin="10,0"
                      Click="MenuButton_Click">
                    <materialDesign:PackIcon Kind="Menu" Width="24" Height="24"/>
                </Button>

                <!-- Title -->
                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                    <materialDesign:PackIcon Kind="Store" 
                                           Width="32" Height="32" 
                                           Foreground="White"
                                           Margin="0,0,10,0"/>
                    <TextBlock Text="نظام إدارة المخزون" 
                             FontSize="20" 
                             FontWeight="Bold"
                             Foreground="White"
                             VerticalAlignment="Center"/>
                </StackPanel>

                <!-- User Info -->
                <StackPanel Grid.Column="2" Orientation="Horizontal" VerticalAlignment="Center" Margin="10,0">
                    <TextBlock x:Name="UserNameTextBlock"
                             Text="مرحباً، مدير النظام"
                             Foreground="White"
                             FontSize="14"
                             VerticalAlignment="Center"
                             Margin="0,0,10,0"/>
                    <Button x:Name="LogoutButton"
                          Style="{StaticResource MaterialDesignIconButton}"
                          Foreground="White"
                          ToolTip="تسجيل الخروج"
                          Click="LogoutButton_Click">
                        <materialDesign:PackIcon Kind="Logout" Width="20" Height="20"/>
                    </Button>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Main Content -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Sidebar -->
            <Border Grid.Column="0" 
                  x:Name="SidebarBorder"
                  Background="{StaticResource SurfaceBrush}"
                  Width="250"
                  BorderBrush="#E0E0E0"
                  BorderThickness="0,0,1,0">
                
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Orientation="Vertical" Margin="0,10">

                        <!-- Dashboard -->
                        <Button x:Name="DashboardButton"
                              Style="{StaticResource MaterialDesignFlatButton}"
                              HorizontalAlignment="Stretch"
                              HorizontalContentAlignment="Right"
                              Padding="20,15"
                              Click="DashboardButton_Click">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="ViewDashboard" 
                                                       Width="20" Height="20" 
                                                       Margin="0,0,10,0"/>
                                <TextBlock Text="لوحة التحكم" FontSize="14"/>
                            </StackPanel>
                        </Button>

                        <!-- Products -->
                        <Button x:Name="ProductsButton"
                              Style="{StaticResource MaterialDesignFlatButton}"
                              HorizontalAlignment="Stretch"
                              HorizontalContentAlignment="Right"
                              Padding="20,15"
                              Click="ProductsButton_Click">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Package" 
                                                       Width="20" Height="20" 
                                                       Margin="0,0,10,0"/>
                                <TextBlock Text="المنتجات" FontSize="14"/>
                            </StackPanel>
                        </Button>

                        <!-- Categories -->
                        <Button x:Name="CategoriesButton"
                              Style="{StaticResource MaterialDesignFlatButton}"
                              HorizontalAlignment="Stretch"
                              HorizontalContentAlignment="Right"
                              Padding="20,15"
                              Click="CategoriesButton_Click">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Tag" 
                                                       Width="20" Height="20" 
                                                       Margin="0,0,10,0"/>
                                <TextBlock Text="الفئات" FontSize="14"/>
                            </StackPanel>
                        </Button>

                        <!-- Inventory -->
                        <Button x:Name="InventoryButton"
                              Style="{StaticResource MaterialDesignFlatButton}"
                              HorizontalAlignment="Stretch"
                              HorizontalContentAlignment="Right"
                              Padding="20,15"
                              Click="InventoryButton_Click">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Warehouse" 
                                                       Width="20" Height="20" 
                                                       Margin="0,0,10,0"/>
                                <TextBlock Text="إدارة المخزون" FontSize="14"/>
                            </StackPanel>
                        </Button>

                        <!-- Reports -->
                        <Button x:Name="ReportsButton"
                              Style="{StaticResource MaterialDesignFlatButton}"
                              HorizontalAlignment="Stretch"
                              HorizontalContentAlignment="Right"
                              Padding="20,15"
                              Click="ReportsButton_Click">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="ChartLine" 
                                                       Width="20" Height="20" 
                                                       Margin="0,0,10,0"/>
                                <TextBlock Text="التقارير" FontSize="14"/>
                            </StackPanel>
                        </Button>

                        <!-- Users (Admin only) -->
                        <Button x:Name="UsersButton"
                              Style="{StaticResource MaterialDesignFlatButton}"
                              HorizontalAlignment="Stretch"
                              HorizontalContentAlignment="Right"
                              Padding="20,15"
                              Click="UsersButton_Click">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="AccountGroup" 
                                                       Width="20" Height="20" 
                                                       Margin="0,0,10,0"/>
                                <TextBlock Text="المستخدمين" FontSize="14"/>
                            </StackPanel>
                        </Button>

                        <!-- Settings -->
                        <Button x:Name="SettingsButton"
                              Style="{StaticResource MaterialDesignFlatButton}"
                              HorizontalAlignment="Stretch"
                              HorizontalContentAlignment="Right"
                              Padding="20,15"
                              Click="SettingsButton_Click">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Settings" 
                                                       Width="20" Height="20" 
                                                       Margin="0,0,10,0"/>
                                <TextBlock Text="الإعدادات" FontSize="14"/>
                            </StackPanel>
                        </Button>

                    </StackPanel>
                </ScrollViewer>
            </Border>

            <!-- Content Area -->
            <Border Grid.Column="1" Background="{StaticResource BackgroundBrush}">
                <Frame x:Name="MainFrame" 
                     NavigationUIVisibility="Hidden"
                     Background="Transparent"/>
            </Border>

        </Grid>

        <!-- Status Bar -->
        <Border Grid.Row="2" Background="{StaticResource SurfaceBrush}" Height="30" BorderBrush="#E0E0E0" BorderThickness="0,1,0,0">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock x:Name="StatusTextBlock"
                         Grid.Column="0"
                         Text="جاهز"
                         VerticalAlignment="Center"
                         Margin="10,0"
                         FontSize="12"
                         Foreground="{StaticResource TextSecondaryBrush}"/>

                <TextBlock Grid.Column="1"
                         x:Name="DateTimeTextBlock"
                         VerticalAlignment="Center"
                         Margin="10,0"
                         FontSize="12"
                         Foreground="{StaticResource TextSecondaryBrush}"/>
            </Grid>
        </Border>

    </Grid>
</Window>
