<?xml version='1.0' encoding='utf-8'?>
<SettingsFile xmlns="http://schemas.microsoft.com/VisualStudio/2004/01/settings" CurrentProfile="(Default)" GeneratedClassNamespace="InventoryManagement.Properties" GeneratedClassName="Settings">
  <Profiles />
  <Settings>
    <Setting Name="RememberUsername" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="RememberMe" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">False</Value>
    </Setting>
    <Setting Name="WindowWidth" Type="System.Double" Scope="User">
      <Value Profile="(Default)">1200</Value>
    </Setting>
    <Setting Name="WindowHeight" Type="System.Double" Scope="User">
      <Value Profile="(Default)">800</Value>
    </Setting>
    <Setting Name="WindowState" Type="System.Windows.WindowState" Scope="User">
      <Value Profile="(Default)">Normal</Value>
    </Setting>
  </Settings>
</SettingsFile>
