<Window x:Class="InventoryManagement.Views.LoginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="تسجيل الدخول - نظام إدارة المخزون"
        Height="500" Width="400"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        Background="{StaticResource BackgroundBrush}"
        FlowDirection="RightToLeft">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Main Content -->
        <Border Grid.Row="0" Style="{StaticResource CardStyle}" Margin="40">
            <StackPanel Orientation="Vertical" HorizontalAlignment="Center">
                
                <!-- Logo and Title -->
                <StackPanel Orientation="Vertical" HorizontalAlignment="Center" Margin="0,20,0,30">
                    <materialDesign:PackIcon Kind="Store" 
                                           Width="80" Height="80" 
                                           Foreground="{StaticResource PrimaryBrush}"
                                           HorizontalAlignment="Center"/>
                    <TextBlock Text="نظام إدارة المخزون" 
                             FontSize="24" 
                             FontWeight="Bold"
                             Foreground="{StaticResource TextPrimaryBrush}"
                             HorizontalAlignment="Center"
                             Margin="0,10,0,0"/>
                    <TextBlock Text="Stralog SRL" 
                             FontSize="14" 
                             Foreground="{StaticResource TextSecondaryBrush}"
                             HorizontalAlignment="Center"
                             Margin="0,5,0,0"/>
                </StackPanel>

                <!-- Login Form -->
                <StackPanel Orientation="Vertical" Width="280">
                    
                    <!-- Username -->
                    <TextBox x:Name="UsernameTextBox"
                           materialDesign:HintAssist.Hint="اسم المستخدم"
                           materialDesign:HintAssist.IsFloating="True"
                           Style="{StaticResource ModernTextBoxStyle}"
                           Margin="0,10"
                           Text="admin"/>

                    <!-- Password -->
                    <PasswordBox x:Name="PasswordBox"
                               materialDesign:HintAssist.Hint="كلمة المرور"
                               materialDesign:HintAssist.IsFloating="True"
                               Style="{StaticResource MaterialDesignOutlinedPasswordBox}"
                               Margin="0,10"
                               Height="40"/>

                    <!-- Remember Me -->
                    <CheckBox x:Name="RememberMeCheckBox"
                            Content="تذكرني"
                            Margin="0,15,0,0"
                            Style="{StaticResource MaterialDesignCheckBox}"/>

                    <!-- Login Button -->
                    <Button x:Name="LoginButton"
                          Content="تسجيل الدخول"
                          Style="{StaticResource PrimaryButtonStyle}"
                          Margin="0,20,0,10"
                          Height="45"
                          FontSize="16"
                          Click="LoginButton_Click"/>

                    <!-- Error Message -->
                    <TextBlock x:Name="ErrorMessageTextBlock"
                             Foreground="{StaticResource ErrorBrush}"
                             FontSize="12"
                             HorizontalAlignment="Center"
                             Margin="0,10,0,0"
                             Visibility="Collapsed"
                             TextWrapping="Wrap"/>

                    <!-- Loading Indicator -->
                    <ProgressBar x:Name="LoadingProgressBar"
                               Style="{StaticResource MaterialDesignCircularProgressBar}"
                               Width="30" Height="30"
                               IsIndeterminate="True"
                               Visibility="Collapsed"
                               Margin="0,10,0,0"/>

                </StackPanel>

            </StackPanel>
        </Border>

        <!-- Footer -->
        <Border Grid.Row="1" Background="{StaticResource PrimaryBrush}" Height="40">
            <TextBlock Text="© 2024 Stralog SRL - جميع الحقوق محفوظة"
                     Foreground="White"
                     FontSize="12"
                     HorizontalAlignment="Center"
                     VerticalAlignment="Center"/>
        </Border>

    </Grid>
</Window>
