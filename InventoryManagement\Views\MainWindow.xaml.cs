using System;
using System.Windows;
using System.Windows.Threading;
using InventoryManagement.Helpers;
using InventoryManagement.Views.Pages;

namespace InventoryManagement.Views
{
    public partial class MainWindow : Window
    {
        private DispatcherTimer _timer;
        private bool _isSidebarCollapsed = false;

        public MainWindow()
        {
            InitializeComponent();
            InitializeWindow();
            LoadDashboard();
        }

        private void InitializeWindow()
        {
            // Set user info
            if (SessionManager.IsLoggedIn)
            {
                UserNameTextBlock.Text = $"مرحباً، {SessionManager.GetUserDisplayName()}";
            }

            // Hide Users button if not admin
            if (!SessionManager.IsAdmin)
            {
                UsersButton.Visibility = Visibility.Collapsed;
            }

            // Initialize timer for status bar
            _timer = new DispatcherTimer();
            _timer.Interval = TimeSpan.FromSeconds(1);
            _timer.Tick += Timer_Tick;
            _timer.Start();

            // Load window settings
            LoadWindowSettings();
        }

        private void Timer_Tick(object sender, EventArgs e)
        {
            DateTimeTextBlock.Text = DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss");
        }

        private void LoadWindowSettings()
        {
            try
            {
                Width = Properties.Settings.Default.WindowWidth;
                Height = Properties.Settings.Default.WindowHeight;
                WindowState = Properties.Settings.Default.WindowState;
            }
            catch
            {
                // Use default values if settings are corrupted
                Width = 1200;
                Height = 800;
                WindowState = WindowState.Normal;
            }
        }

        private void SaveWindowSettings()
        {
            try
            {
                Properties.Settings.Default.WindowWidth = Width;
                Properties.Settings.Default.WindowHeight = Height;
                Properties.Settings.Default.WindowState = WindowState;
                Properties.Settings.Default.Save();
            }
            catch
            {
                // Ignore save errors
            }
        }

        private void MenuButton_Click(object sender, RoutedEventArgs e)
        {
            ToggleSidebar();
        }

        private void ToggleSidebar()
        {
            if (_isSidebarCollapsed)
            {
                SidebarBorder.Width = 250;
                _isSidebarCollapsed = false;
            }
            else
            {
                SidebarBorder.Width = 60;
                _isSidebarCollapsed = true;
            }
        }

        private void LogoutButton_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("هل أنت متأكد من تسجيل الخروج؟", "تأكيد", 
                MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                SessionManager.Logout();
                
                var loginWindow = new LoginWindow();
                loginWindow.Show();
                
                Close();
            }
        }

        private void DashboardButton_Click(object sender, RoutedEventArgs e)
        {
            LoadDashboard();
            UpdateStatus("لوحة التحكم");
        }

        private void ProductsButton_Click(object sender, RoutedEventArgs e)
        {
            MainFrame.Navigate(new ProductsPage());
            UpdateStatus("إدارة المنتجات");
        }

        private void CategoriesButton_Click(object sender, RoutedEventArgs e)
        {
            MainFrame.Navigate(new CategoriesPage());
            UpdateStatus("إدارة الفئات");
        }

        private void InventoryButton_Click(object sender, RoutedEventArgs e)
        {
            MainFrame.Navigate(new InventoryPage());
            UpdateStatus("إدارة المخزون");
        }

        private void ReportsButton_Click(object sender, RoutedEventArgs e)
        {
            MainFrame.Navigate(new ReportsPage());
            UpdateStatus("التقارير");
        }

        private void UsersButton_Click(object sender, RoutedEventArgs e)
        {
            if (SessionManager.IsAdmin)
            {
                MainFrame.Navigate(new UsersPage());
                UpdateStatus("إدارة المستخدمين");
            }
        }

        private void SettingsButton_Click(object sender, RoutedEventArgs e)
        {
            MainFrame.Navigate(new SettingsPage());
            UpdateStatus("الإعدادات");
        }

        private void LoadDashboard()
        {
            MainFrame.Navigate(new DashboardPage());
            UpdateStatus("لوحة التحكم");
        }

        private void UpdateStatus(string status)
        {
            StatusTextBlock.Text = status;
        }

        protected override void OnClosed(EventArgs e)
        {
            SaveWindowSettings();
            _timer?.Stop();
            Application.Current.Shutdown();
            base.OnClosed(e);
        }
    }
}
