using System.Windows;
using InventoryManagement.Data;
using Microsoft.EntityFrameworkCore;
using System.IO;
using System;

namespace InventoryManagement
{
    public partial class App : Application
    {
        protected override void OnStartup(StartupEventArgs e)
        {
            base.OnStartup(e);
            
            // Initialize database
            InitializeDatabase();
        }

        private void InitializeDatabase()
        {
            try
            {
                using var context = new InventoryContext();
                
                // Ensure database is created
                context.Database.EnsureCreated();
                
                // Run any pending migrations
                if (context.Database.GetPendingMigrations().Any())
                {
                    context.Database.Migrate();
                }
                
                // Seed initial data if needed
                SeedInitialData(context);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة قاعدة البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
                Shutdown();
            }
        }

        private void SeedInitialData(InventoryContext context)
        {
            // Add default admin user if no users exist
            if (!context.Users.Any())
            {
                var adminUser = new Models.User
                {
                    Username = "admin",
                    Password = BCrypt.Net.BCrypt.HashPassword("admin123"),
                    FullName = "مدير النظام",
                    Role = "Admin",
                    IsActive = true,
                    CreatedDate = DateTime.Now
                };
                
                context.Users.Add(adminUser);
            }

            // Add default categories if none exist
            if (!context.Categories.Any())
            {
                var categories = new[]
                {
                    new Models.Category { Name = "إلكترونيات", Description = "الأجهزة الإلكترونية والكهربائية" },
                    new Models.Category { Name = "ملابس", Description = "الملابس والأزياء" },
                    new Models.Category { Name = "طعام ومشروبات", Description = "المواد الغذائية والمشروبات" },
                    new Models.Category { Name = "كتب وقرطاسية", Description = "الكتب والأدوات المكتبية" },
                    new Models.Category { Name = "منزل وحديقة", Description = "أدوات المنزل والحديقة" }
                };

                context.Categories.AddRange(categories);
            }

            context.SaveChanges();
        }
    }
}
