using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace InventoryManagement.Models
{
    public class Product
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        [StringLength(500)]
        public string Description { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string SKU { get; set; } = string.Empty; // Stock Keeping Unit

        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal PurchasePrice { get; set; }

        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal SellingPrice { get; set; }

        [Required]
        public int Quantity { get; set; }

        [Required]
        public int MinimumStock { get; set; } = 5;

        [Required]
        public int CategoryId { get; set; }

        [ForeignKey("CategoryId")]
        public virtual Category Category { get; set; } = null!;

        [StringLength(200)]
        public string Supplier { get; set; } = string.Empty;

        [StringLength(50)]
        public string Unit { get; set; } = "قطعة"; // وحدة القياس

        public string? ImagePath { get; set; }

        [StringLength(100)]
        public string Location { get; set; } = string.Empty; // موقع التخزين

        public bool IsActive { get; set; } = true;

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        public DateTime? LastUpdated { get; set; }

        [StringLength(500)]
        public string Notes { get; set; } = string.Empty;

        // Calculated Properties
        [NotMapped]
        public decimal ProfitMargin => SellingPrice - PurchasePrice;

        [NotMapped]
        public decimal ProfitPercentage => PurchasePrice > 0 ? (ProfitMargin / PurchasePrice) * 100 : 0;

        [NotMapped]
        public bool IsLowStock => Quantity <= MinimumStock;

        [NotMapped]
        public string StockStatus
        {
            get
            {
                if (Quantity == 0) return "نفد المخزون";
                if (IsLowStock) return "مخزون منخفض";
                return "متوفر";
            }
        }

        [NotMapped]
        public decimal TotalValue => Quantity * PurchasePrice;

        // Navigation Properties
        public virtual ICollection<Transaction> Transactions { get; set; } = new List<Transaction>();
    }
}
