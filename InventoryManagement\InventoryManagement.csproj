<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <UseWPF>true</UseWPF>
    <ApplicationIcon>icon.ico</ApplicationIcon>
    <AssemblyTitle>نظام إدارة المخزون</AssemblyTitle>
    <AssemblyDescription>برنامج شامل لإدارة المخزون والمنتجات</AssemblyDescription>
    <AssemblyCompany>Stralog SRL</AssemblyCompany>
    <AssemblyProduct>Inventory Management System</AssemblyProduct>
    <AssemblyCopyright>Copyright © 2024 Stralog SRL</AssemblyCopyright>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="7.0.14" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="7.0.14" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="7.0.14" />
    <PackageReference Include="MaterialDesignThemes" Version="4.9.0" />
    <PackageReference Include="MaterialDesignColors" Version="2.1.4" />
    <PackageReference Include="ClosedXML" Version="0.102.1" />
    <PackageReference Include="iTextSharp" Version="********" />
    <PackageReference Include="System.Drawing.Common" Version="7.0.0" />
    <PackageReference Include="BCrypt.Net-Next" Version="4.0.3" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Models\" />
    <Folder Include="Views\" />
    <Folder Include="ViewModels\" />
    <Folder Include="Services\" />
    <Folder Include="Data\" />
    <Folder Include="Resources\" />
    <Folder Include="Helpers\" />
  </ItemGroup>

</Project>
