using System;
using System.Windows;
using System.Windows.Input;
using InventoryManagement.Services;
using InventoryManagement.Helpers;

namespace InventoryManagement.Views
{
    public partial class LoginWindow : Window
    {
        private readonly UserService _userService;

        public LoginWindow()
        {
            InitializeComponent();
            _userService = new UserService();
            
            // Set default password for demo
            PasswordBox.Password = "admin123";
            
            // Handle Enter key press
            KeyDown += LoginWindow_KeyDown;
            UsernameTextBox.KeyDown += Control_KeyDown;
            PasswordBox.KeyDown += Control_KeyDown;
        }

        private void LoginWindow_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                LoginButton_Click(sender, e);
            }
        }

        private void Control_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                LoginButton_Click(sender, e);
            }
        }

        private async void LoginButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Validate input
                if (string.IsNullOrWhiteSpace(UsernameTextBox.Text))
                {
                    ShowError("يرجى إدخال اسم المستخدم");
                    UsernameTextBox.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(PasswordBox.Password))
                {
                    ShowError("يرجى إدخال كلمة المرور");
                    PasswordBox.Focus();
                    return;
                }

                // Show loading
                SetLoading(true);
                HideError();

                // Authenticate user
                var user = await _userService.AuthenticateAsync(UsernameTextBox.Text.Trim(), PasswordBox.Password);

                if (user != null)
                {
                    // Store current user session
                    SessionManager.CurrentUser = user;
                    
                    // Save login credentials if remember me is checked
                    if (RememberMeCheckBox.IsChecked == true)
                    {
                        Properties.Settings.Default.RememberUsername = UsernameTextBox.Text.Trim();
                        Properties.Settings.Default.RememberMe = true;
                        Properties.Settings.Default.Save();
                    }
                    else
                    {
                        Properties.Settings.Default.RememberUsername = string.Empty;
                        Properties.Settings.Default.RememberMe = false;
                        Properties.Settings.Default.Save();
                    }

                    // Open main window
                    var mainWindow = new MainWindow();
                    mainWindow.Show();
                    
                    // Close login window
                    Close();
                }
                else
                {
                    ShowError("اسم المستخدم أو كلمة المرور غير صحيحة");
                    PasswordBox.Clear();
                    PasswordBox.Focus();
                }
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تسجيل الدخول: {ex.Message}");
            }
            finally
            {
                SetLoading(false);
            }
        }

        private void ShowError(string message)
        {
            ErrorMessageTextBlock.Text = message;
            ErrorMessageTextBlock.Visibility = Visibility.Visible;
        }

        private void HideError()
        {
            ErrorMessageTextBlock.Visibility = Visibility.Collapsed;
        }

        private void SetLoading(bool isLoading)
        {
            LoginButton.IsEnabled = !isLoading;
            UsernameTextBox.IsEnabled = !isLoading;
            PasswordBox.IsEnabled = !isLoading;
            RememberMeCheckBox.IsEnabled = !isLoading;
            
            LoadingProgressBar.Visibility = isLoading ? Visibility.Visible : Visibility.Collapsed;
        }

        private void Window_Loaded(object sender, RoutedEventArgs e)
        {
            // Load saved credentials if remember me was checked
            if (Properties.Settings.Default.RememberMe)
            {
                UsernameTextBox.Text = Properties.Settings.Default.RememberUsername;
                RememberMeCheckBox.IsChecked = true;
                PasswordBox.Focus();
            }
            else
            {
                UsernameTextBox.Focus();
            }
        }

        protected override void OnClosed(EventArgs e)
        {
            _userService?.Dispose();
            base.OnClosed(e);
        }
    }
}
