using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace InventoryManagement.Models
{
    public class Transaction
    {
        [Key]
        public int Id { get; set; }

        [Required]
        public int ProductId { get; set; }

        [ForeignKey("ProductId")]
        public virtual Product Product { get; set; } = null!;

        [Required]
        [StringLength(20)]
        public string Type { get; set; } = string.Empty; // "IN" للإدخال، "OUT" للإخراج

        [Required]
        public int Quantity { get; set; }

        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal UnitPrice { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalAmount => Quantity * UnitPrice;

        [StringLength(500)]
        public string Reason { get; set; } = string.Empty; // سبب المعاملة

        [StringLength(200)]
        public string Reference { get; set; } = string.Empty; // رقم مرجعي

        public DateTime TransactionDate { get; set; } = DateTime.Now;

        [Required]
        public int UserId { get; set; }

        [ForeignKey("UserId")]
        public virtual User User { get; set; } = null!;

        [StringLength(500)]
        public string Notes { get; set; } = string.Empty;

        // Calculated Properties
        [NotMapped]
        public string TypeDescription => Type switch
        {
            "IN" => "إدخال",
            "OUT" => "إخراج",
            "ADJUSTMENT" => "تعديل",
            "RETURN" => "إرجاع",
            _ => "غير محدد"
        };

        [NotMapped]
        public string FormattedDate => TransactionDate.ToString("dd/MM/yyyy HH:mm");
    }
}
