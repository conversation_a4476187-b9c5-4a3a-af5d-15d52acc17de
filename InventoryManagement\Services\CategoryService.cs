using InventoryManagement.Data;
using InventoryManagement.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace InventoryManagement.Services
{
    public class CategoryService
    {
        private readonly InventoryContext _context;

        public CategoryService()
        {
            _context = new InventoryContext();
        }

        public async Task<List<Category>> GetAllCategoriesAsync()
        {
            return await _context.Categories
                .Where(c => c.IsActive)
                .OrderBy(c => c.Name)
                .ToListAsync();
        }

        public async Task<Category?> GetCategoryByIdAsync(int id)
        {
            return await _context.Categories
                .Include(c => c.Products)
                .FirstOrDefaultAsync(c => c.Id == id);
        }

        public async Task<bool> AddCategoryAsync(Category category)
        {
            try
            {
                // Check if category name already exists
                var existingCategory = await _context.Categories
                    .FirstOrDefaultAsync(c => c.Name == category.Name && c.IsActive);
                if (existingCategory != null)
                    return false;

                category.CreatedDate = DateTime.Now;
                _context.Categories.Add(category);
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> UpdateCategoryAsync(Category category)
        {
            try
            {
                // Check if category name already exists for another category
                var existingCategory = await _context.Categories
                    .FirstOrDefaultAsync(c => c.Name == category.Name && c.Id != category.Id && c.IsActive);
                if (existingCategory != null)
                    return false;

                _context.Categories.Update(category);
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> DeleteCategoryAsync(int id)
        {
            try
            {
                var category = await GetCategoryByIdAsync(id);
                if (category == null)
                    return false;

                // Check if category has products
                var hasProducts = await _context.Products
                    .AnyAsync(p => p.CategoryId == id && p.IsActive);

                if (hasProducts)
                {
                    // Soft delete - mark as inactive
                    category.IsActive = false;
                    _context.Categories.Update(category);
                }
                else
                {
                    // Hard delete
                    _context.Categories.Remove(category);
                }

                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<int> GetProductsCountByCategoryAsync(int categoryId)
        {
            return await _context.Products
                .Where(p => p.CategoryId == categoryId && p.IsActive)
                .CountAsync();
        }

        public void Dispose()
        {
            _context?.Dispose();
        }
    }
}
