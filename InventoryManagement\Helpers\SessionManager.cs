using InventoryManagement.Models;

namespace InventoryManagement.Helpers
{
    public static class SessionManager
    {
        public static User? CurrentUser { get; set; }

        public static bool IsLoggedIn => CurrentUser != null;

        public static bool IsAdmin => CurrentUser?.Role == "Admin";

        public static bool IsManager => CurrentUser?.Role == "Manager" || IsAdmin;

        public static void Logout()
        {
            CurrentUser = null;
        }

        public static string GetUserDisplayName()
        {
            return CurrentUser?.FullName ?? "غير معروف";
        }

        public static int GetUserId()
        {
            return CurrentUser?.Id ?? 0;
        }
    }
}
